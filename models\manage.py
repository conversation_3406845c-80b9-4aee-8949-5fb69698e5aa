# -*- coding: utf-8 -*-
"""
管理员数据模型
提供管理员登录验证功能
"""

from utils.database_helper import db_helper
import logging

class ManageModel:
    """管理员数据模型类"""
    
    def __init__(self):
        self.table_name = "Manage"
        self.logger = logging.getLogger(__name__)
    
    def authenticate(self, gno, password):
        """
        验证管理员登录
        
        Args:
            gno (str): 管理员编号
            password (str): 密码
            
        Returns:
            dict: 管理员信息字典，如果验证失败返回None
        """
        try:
            sql = "SELECT * FROM Manage WHERE Gno = ? AND Gpassword = ?"
            result = db_helper.execute_query(sql, (gno, password))
            
            if result:
                self.logger.info(f"管理员 {gno} 登录成功")
                return result[0]
            else:
                self.logger.warning(f"管理员 {gno} 登录失败：用户名或密码错误")
                return None
                
        except Exception as e:
            self.logger.error(f"管理员登录验证失败: {str(e)}")
            raise Exception(f"登录验证失败: {str(e)}")
    
    def get_by_id(self, gno):
        """
        根据管理员编号获取管理员信息
        
        Args:
            gno (str): 管理员编号
            
        Returns:
            dict: 管理员信息字典，如果不存在返回None
        """
        try:
            sql = "SELECT * FROM Manage WHERE Gno = ?"
            result = db_helper.execute_query(sql, (gno,))
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"获取管理员信息失败: {str(e)}")
            raise

# 全局管理员模型实例
manage_model = ManageModel()
