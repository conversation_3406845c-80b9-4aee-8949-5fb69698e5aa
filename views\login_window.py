# -*- coding: utf-8 -*-
"""
管理员登录窗口
提供管理员身份验证界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from models.manage import manage_model

class LoginWindow:
    """登录窗口类"""
    
    def __init__(self):
        self.result = None  # 登录结果
        self.admin_info = None  # 管理员信息

        # 创建登录窗口
        self.window = tk.Tk()
        self.window.title("学生管理系统 - 管理员登录")
        self.window.geometry("400x300")
        self.window.resizable(False, False)

        # 设置窗口关闭事件处理
        self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)

        # 设置窗口居中
        self.center_window()

        # 创建界面
        self.create_widgets()

        # 设置焦点
        self.username_entry.focus()
    
    def center_window(self):
        """窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="40")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="学生管理系统", 
            font=("微软雅黑", 18, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        subtitle_label = ttk.Label(
            main_frame, 
            text="管理员登录", 
            font=("微软雅黑", 12)
        )
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 30))
        
        # 用户名
        ttk.Label(main_frame, text="管理员编号:").grid(row=2, column=0, sticky=tk.W, pady=10)
        
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(main_frame, textvariable=self.username_var, width=20, font=("微软雅黑", 11))
        self.username_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=10, padx=(10, 0))
        
        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=3, column=0, sticky=tk.W, pady=10)
        
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=20, font=("微软雅黑", 11))
        self.password_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=10, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(30, 0))
        
        # 登录按钮
        login_btn = ttk.Button(
            button_frame, 
            text="登录", 
            command=self.login,
            width=12
        )
        login_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 退出按钮
        exit_btn = ttk.Button(
            button_frame, 
            text="退出", 
            command=self.exit_app,
            width=12
        )
        exit_btn.grid(row=0, column=1)
        
        # 提示信息
        info_label = ttk.Label(
            main_frame,
            text="默认管理员账号：admin，密码：123456",
            font=("微软雅黑", 9),
            foreground="gray"
        )
        info_label.grid(row=5, column=0, columnspan=2, pady=(20, 0))

        # 状态标签（用于显示登录状态）
        self.status_label = ttk.Label(
            main_frame,
            text="",
            font=("微软雅黑", 9),
            foreground="red"
        )
        self.status_label.grid(row=6, column=0, columnspan=2, pady=(10, 0))
        
        # 绑定回车键
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())
        self.password_entry.bind('<Return>', lambda e: self.login())
    
    def login(self):
        """登录验证"""
        print("=== 登录按钮被点击 ===")  # 调试输出

        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        print(f"输入的账号: '{username}'")  # 调试输出
        print(f"输入的密码: '{password}'")  # 调试输出

        # 清除之前的状态信息
        self.status_label.config(text="")
        self.window.update()  # 强制更新界面

        # 基本验证
        if not username:
            print("账号为空")  # 调试输出
            self.status_label.config(text="请输入管理员编号", foreground="red")
            self.window.update()  # 强制更新界面
            self.username_entry.focus()
            return

        if not password:
            print("密码为空")  # 调试输出
            self.status_label.config(text="请输入密码", foreground="red")
            self.window.update()  # 强制更新界面
            self.password_entry.focus()
            return

        # 显示登录中状态
        print("开始验证登录...")  # 调试输出
        self.status_label.config(text="正在验证登录信息...", foreground="blue")
        self.window.update()  # 强制更新界面

        try:
            # 验证登录
            print("调用 manage_model.authenticate...")  # 调试输出
            admin_info = manage_model.authenticate(username, password)
            print(f"验证结果: {admin_info}")  # 调试输出

            if admin_info:
                print("登录成功！")  # 调试输出
                self.admin_info = admin_info
                self.result = True
                admin_name = admin_info.get('Gname', '').strip()
                self.status_label.config(text=f"登录成功！欢迎您，{admin_name}", foreground="green")
                self.window.update()  # 强制更新界面
                # 延迟一下让用户看到成功信息
                self.window.after(1000, self.window.destroy)
            else:
                print("登录失败：账号或密码错误")  # 调试输出
                self.status_label.config(text="管理员编号或密码错误，请重新输入", foreground="red")
                self.window.update()  # 强制更新界面
                self.password_var.set("")
                self.username_entry.focus()

        except Exception as e:
            print(f"登录异常: {str(e)}")  # 调试输出
            import traceback
            traceback.print_exc()  # 打印完整的异常堆栈
            self.status_label.config(text=f"登录错误：{str(e)}", foreground="red")
            self.window.update()  # 强制更新界面
    
    def exit_app(self):
        """退出应用程序"""
        self.result = False
        self.window.destroy()

    def on_window_close(self):
        """处理窗口关闭事件"""
        self.result = False
        self.admin_info = None
        self.window.destroy()

    def show(self):
        """显示登录窗口并等待结果"""
        try:
            self.window.mainloop()
        except KeyboardInterrupt:
            # 处理Ctrl+C中断
            self.result = False
            self.admin_info = None
        except Exception as e:
            # 处理其他异常
            print(f"登录窗口异常: {str(e)}")
            self.result = False
            self.admin_info = None
        finally:
            # 确保窗口被销毁
            try:
                if self.window.winfo_exists():
                    self.window.destroy()
            except:
                pass

        return self.result, self.admin_info
