# -*- coding: utf-8 -*-
"""
选课管理面板
提供选课数据的增删改查界面和分页功能（内嵌式）
"""

from views.base_panel import BasePanel
from models.sc import sc_model

class SCPanel(BasePanel):
    """选课管理面板类"""

    def get_columns(self):
        """返回表格列定义"""
        return ('Cno', 'Cname', 'Sno', 'Sname', 'Grade', 'Cpno')

    def get_column_configs(self):
        """返回列配置字典"""
        return {
            'Cno': ('课程编号', 100),
            'Cname': ('课程名称', 150),
            'Sno': ('学号', 100),
            'Sname': ('学生姓名', 100),
            'Grade': ('成绩', 80),
            'Cpno': ('先修课程', 100)
        }

    def get_model(self):
        """返回数据模型对象"""
        return sc_model

    def get_entity_name(self):
        """返回实体名称"""
        return "选课记录"

    def get_dialog_class(self):
        """返回对话框类"""
        from views.sc_window import SCDialog
        return SCDialog

    def extract_row_data(self, data_item):
        """从数据项中提取行显示数据"""
        return (
            data_item.get('Cno', ''),
            data_item.get('Cname', ''),
            data_item.get('Sno', ''),
            data_item.get('Sname', ''),
            data_item.get('Grade', ''),
            data_item.get('Cpno', '')
        )

    def get_primary_key(self, tree_item_values):
        """从表格行数据中提取主键（复合主键）"""
        return (tree_item_values[0], tree_item_values[2])  # (Cno, Sno)

    def get_entity_display_name(self, values):
        """获取实体的显示名称（用于删除确认）"""
        return f"学生：{values[3]}（{values[2]}）\n课程：{values[1]}（{values[0]}）"
