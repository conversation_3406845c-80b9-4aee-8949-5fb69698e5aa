# -*- coding: utf-8 -*-
"""
学生管理系统主程序
提供系统启动和数据库连接测试功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from views.login_window import LoginWindow
from views.main_window import MainWindow
from config.database import db_config


def main():
    """主函数"""
    # 创建根窗口（隐藏）
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 显示登录窗口
    try:
        login_window = LoginWindow()
        login_result, admin_info = login_window.show()

        if login_result and admin_info:
            # 登录成功，启动主应用程序
            try:
                app = MainWindow(admin_info)
                app.run()
            except KeyboardInterrupt:
                print("用户中断程序运行")
            except Exception as e:
                messagebox.showerror("系统错误", f"主程序运行失败：{str(e)}")
        else:
            # 用户取消登录或登录失败
            print("用户取消登录，程序退出")

    except KeyboardInterrupt:
        print("用户中断登录过程")
    except Exception as e:
        messagebox.showerror("系统错误", f"系统启动失败：{str(e)}")

    print("感谢使用学生管理系统！")

if __name__ == "__main__":
    main()
