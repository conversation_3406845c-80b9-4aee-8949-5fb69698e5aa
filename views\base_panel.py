# -*- coding: utf-8 -*-
"""
基础面板类
提供所有管理面板的通用功能和界面组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
from abc import ABC, abstractmethod

class BasePanel(ABC):
    """管理面板基类"""
    
    def __init__(self, parent):
        self.parent = parent
        
        # 数据相关属性
        self.current_page = 1
        self.page_size = 10
        self.total_pages = 0
        self.search_term = ""
        
        # 创建界面
        self.create_widgets()
        
        # 加载数据
        self.load_data()
    
    # 抽象方法 - 子类必须实现
    @abstractmethod
    def get_columns(self):
        """返回表格列定义"""
        pass
    
    @abstractmethod
    def get_column_configs(self):
        """返回列配置字典 {列名: (显示名称, 宽度)}"""
        pass
    
    @abstractmethod
    def get_model(self):
        """返回数据模型对象"""
        pass
    
    @abstractmethod
    def get_entity_name(self):
        """返回实体名称（用于按钮文本）"""
        pass
    
    @abstractmethod
    def get_dialog_class(self):
        """返回对话框类"""
        pass
    
    @abstractmethod
    def extract_row_data(self, data_item):
        """从数据项中提取行显示数据"""
        pass
    
    @abstractmethod
    def get_primary_key(self, tree_item_values):
        """从表格行数据中提取主键"""
        pass
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.parent, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.parent.columnconfigure(0, weight=1)
        self.parent.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 创建工具栏
        self.create_toolbar(main_frame)
        
        # 创建数据表格
        self.create_treeview(main_frame)
        
        # 创建分页控件
        self.create_pagination(main_frame)
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        toolbar_frame.columnconfigure(1, weight=1)
        
        # 搜索框
        ttk.Label(toolbar_frame, text="搜索:").grid(row=0, column=0, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar_frame, textvariable=self.search_var, width=30)
        search_entry.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
        search_entry.bind('<Return>', lambda e: self.search_data())
        
        # 搜索按钮
        search_btn = ttk.Button(toolbar_frame, text="搜索", command=self.search_data)
        search_btn.grid(row=0, column=2, padx=(0, 10))
        
        # 重置按钮
        reset_btn = ttk.Button(toolbar_frame, text="重置", command=self.reset_search)
        reset_btn.grid(row=0, column=3, padx=(0, 20))
        
        # 操作按钮
        entity_name = self.get_entity_name()
        add_btn = ttk.Button(toolbar_frame, text=f"添加{entity_name}", command=self.add_entity)
        add_btn.grid(row=0, column=4, padx=(0, 5))
        
        edit_btn = ttk.Button(toolbar_frame, text=f"编辑{entity_name}", command=self.edit_entity)
        edit_btn.grid(row=0, column=5, padx=(0, 5))
        
        delete_btn = ttk.Button(toolbar_frame, text=f"删除{entity_name}", command=self.delete_entity)
        delete_btn.grid(row=0, column=6)
    
    def create_treeview(self, parent):
        """创建数据表格"""
        # 创建框架
        tree_frame = ttk.Frame(parent)
        tree_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # 获取列配置
        columns = self.get_columns()
        column_configs = self.get_column_configs()
        
        # 创建Treeview
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        for col in columns:
            heading, width = column_configs[col]
            self.tree.heading(col, text=heading)
            self.tree.column(col, width=width, minwidth=50)
        
        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 绑定双击事件
        self.tree.bind('<Double-1>', lambda e: self.edit_entity())
    
    def create_pagination(self, parent):
        """创建分页控件"""
        page_frame = ttk.Frame(parent)
        page_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 分页信息标签
        self.page_info_label = ttk.Label(page_frame, text="")
        self.page_info_label.grid(row=0, column=0, padx=(0, 20))
        
        # 分页按钮
        self.prev_btn = ttk.Button(page_frame, text="上一页", command=self.prev_page)
        self.prev_btn.grid(row=0, column=1, padx=(0, 5))
        
        self.next_btn = ttk.Button(page_frame, text="下一页", command=self.next_page)
        self.next_btn.grid(row=0, column=2, padx=(0, 20))
        
        # 跳转页面
        ttk.Label(page_frame, text="跳转到:").grid(row=0, column=3, padx=(0, 5))
        
        self.page_var = tk.StringVar()
        page_entry = ttk.Entry(page_frame, textvariable=self.page_var, width=5)
        page_entry.grid(row=0, column=4, padx=(0, 5))
        page_entry.bind('<Return>', lambda e: self.goto_page())
        
        goto_btn = ttk.Button(page_frame, text="跳转", command=self.goto_page)
        goto_btn.grid(row=0, column=5)
    
    def load_data(self):
        """加载数据"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 获取分页数据
            model = self.get_model()
            result = model.get_all_paginated(
                page=self.current_page,
                page_size=self.page_size,
                search_term=self.search_term
            )
            
            # 更新分页信息
            self.total_pages = result['total_pages']
            
            # 插入数据到表格
            for data_item in result['data']:
                row_data = self.extract_row_data(data_item)
                self.tree.insert('', tk.END, values=row_data)
            
            # 更新分页控件
            self.update_pagination_controls(result)
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败：{str(e)}")
    
    def update_pagination_controls(self, result):
        """更新分页控件状态"""
        # 更新页面信息
        info_text = f"第 {result['current_page']} 页，共 {result['total_pages']} 页，总计 {result['total_records']} 条记录"
        self.page_info_label.config(text=info_text)
        
        # 更新按钮状态
        self.prev_btn.config(state=tk.NORMAL if result['has_previous'] else tk.DISABLED)
        self.next_btn.config(state=tk.NORMAL if result['has_next'] else tk.DISABLED)
    
    def search_data(self):
        """搜索数据"""
        self.search_term = self.search_var.get().strip()
        self.current_page = 1
        self.load_data()
    
    def reset_search(self):
        """重置搜索"""
        self.search_var.set("")
        self.search_term = ""
        self.current_page = 1
        self.load_data()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_data()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_data()
    
    def goto_page(self):
        """跳转到指定页面"""
        try:
            page = int(self.page_var.get())
            if 1 <= page <= self.total_pages:
                self.current_page = page
                self.load_data()
                self.page_var.set("")
            else:
                messagebox.showwarning("警告", f"页码必须在 1 到 {self.total_pages} 之间")
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的页码")
    
    def add_entity(self):
        """添加实体"""
        dialog_class = self.get_dialog_class()
        entity_name = self.get_entity_name()
        
        dialog = dialog_class(self.parent, f"添加{entity_name}")
        if dialog.result:
            try:
                model = self.get_model()
                model.create(dialog.result)
                messagebox.showinfo("成功", f"{entity_name}添加成功")
                self.load_data()
            except Exception as e:
                messagebox.showerror("错误", f"添加{entity_name}失败：{str(e)}")
    
    def edit_entity(self):
        """编辑实体"""
        selection = self.tree.selection()
        if not selection:
            entity_name = self.get_entity_name()
            messagebox.showwarning("警告", f"请选择要编辑的{entity_name}")
            return

        # 获取选中的数据
        item = self.tree.item(selection[0])
        primary_key = self.get_primary_key(item['values'])

        try:
            model = self.get_model()
            # 支持复合主键（如选课表）
            if isinstance(primary_key, tuple):
                entity_data = model.get_by_id(*primary_key)
            else:
                entity_data = model.get_by_id(primary_key)

            if entity_data:
                dialog_class = self.get_dialog_class()
                entity_name = self.get_entity_name()

                dialog = dialog_class(self.parent, f"编辑{entity_name}", entity_data)
                if dialog.result:
                    # 支持复合主键的更新
                    if isinstance(primary_key, tuple):
                        model.update(*primary_key, dialog.result)
                    else:
                        model.update(primary_key, dialog.result)
                    messagebox.showinfo("成功", f"{entity_name}信息更新成功")
                    self.load_data()
            else:
                messagebox.showerror("错误", f"未找到{entity_name}信息")
        except Exception as e:
            entity_name = self.get_entity_name()
            messagebox.showerror("错误", f"编辑{entity_name}失败：{str(e)}")
    
    def delete_entity(self):
        """删除实体"""
        selection = self.tree.selection()
        if not selection:
            entity_name = self.get_entity_name()
            messagebox.showwarning("警告", f"请选择要删除的{entity_name}")
            return

        # 获取选中的数据
        item = self.tree.item(selection[0])
        primary_key = self.get_primary_key(item['values'])
        entity_display_name = self.get_entity_display_name(item['values'])

        # 确认删除
        entity_name = self.get_entity_name()
        if messagebox.askyesno("确认删除", f"确定要删除{entity_name} {entity_display_name}吗？\n注意：如果该{entity_name}有关联数据，将无法删除。"):
            try:
                model = self.get_model()
                # 支持复合主键（如选课表）
                if isinstance(primary_key, tuple):
                    model.delete(*primary_key)
                else:
                    model.delete(primary_key)
                messagebox.showinfo("成功", f"{entity_name}删除成功")
                self.load_data()
            except Exception as e:
                messagebox.showerror("错误", f"删除{entity_name}失败：{str(e)}")
    
    def get_entity_display_name(self, values):
        """获取实体的显示名称（用于删除确认）"""
        # 默认实现：返回第二列（通常是名称）
        return f"{values[1]}（{values[0]}）" if len(values) > 1 else values[0]
