# -*- coding: utf-8 -*-
"""
主窗口界面
提供系统主界面和内嵌式模块切换功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from views.teacher_panel import TeacherPanel
from views.student_panel import StudentPanel
from views.course_panel import CoursePanel
from views.sc_panel import SCPanel

class MainWindow:
    """主窗口类"""

    def __init__(self, admin_info):
        self.admin_info = admin_info
        self.root = tk.Tk()
        self.root.title(f"学生管理系统 - {admin_info['Gname']}")
        self.root.geometry("1400x900")
        self.root.resizable(True, True)

        # 设置窗口居中
        self.center_window()

        # 当前显示的面板
        self.current_panel = None

        # 创建界面
        self.create_widgets()

        # 默认显示教师管理
        self.show_teacher_panel()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 顶部标题栏
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        title_frame.columnconfigure(0, weight=1)

        # 标题和用户信息
        title_label = ttk.Label(
            title_frame,
            text="学生管理系统",
            font=("微软雅黑", 20, "bold")
        )
        title_label.grid(row=0, column=0, sticky=tk.W)

        user_label = ttk.Label(
            title_frame,
            text=f"当前用户：{self.admin_info['Gname']} ({self.admin_info['Gno']})",
            font=("微软雅黑", 10)
        )
        user_label.grid(row=0, column=1, sticky=tk.E)

        # 左侧菜单框架
        menu_frame = ttk.LabelFrame(main_frame, text="功能菜单", padding="15")
        menu_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # 右侧内容框架
        self.content_frame = ttk.LabelFrame(main_frame, text="管理内容", padding="10")
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)

        # 创建菜单按钮
        self.create_menu_buttons(menu_frame)
    
    def create_menu_buttons(self, parent):
        """创建菜单按钮"""
        # 按钮样式配置
        button_style = {
            'width': 18,
            'padding': (10, 8)
        }

        # 教师管理按钮
        self.teacher_btn = ttk.Button(
            parent,
            text="教师管理",
            command=self.show_teacher_panel,
            **button_style
        )
        self.teacher_btn.grid(row=0, column=0, pady=8, sticky=tk.W+tk.E)

        # 学生管理按钮
        self.student_btn = ttk.Button(
            parent,
            text="学生管理",
            command=self.show_student_panel,
            **button_style
        )
        self.student_btn.grid(row=1, column=0, pady=8, sticky=tk.W+tk.E)

        # 课程管理按钮
        self.course_btn = ttk.Button(
            parent,
            text="课程管理",
            command=self.show_course_panel,
            **button_style
        )
        self.course_btn.grid(row=2, column=0, pady=8, sticky=tk.W+tk.E)

        # 选课管理按钮
        self.sc_btn = ttk.Button(
            parent,
            text="选课管理",
            command=self.show_sc_panel,
            **button_style
        )
        self.sc_btn.grid(row=3, column=0, pady=8, sticky=tk.W+tk.E)

        # 分隔线
        separator = ttk.Separator(parent, orient='horizontal')
        separator.grid(row=4, column=0, sticky=tk.W+tk.E, pady=20)

        # 退出按钮
        exit_btn = ttk.Button(
            parent,
            text="退出系统",
            command=self.exit_application,
            **button_style
        )
        exit_btn.grid(row=5, column=0, pady=8, sticky=tk.W+tk.E)

        # 配置列权重
        parent.columnconfigure(0, weight=1)
    
    def clear_content_frame(self):
        """清空内容框架"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def update_button_states(self, active_button):
        """更新按钮状态"""
        # 重置所有按钮状态
        buttons = [self.teacher_btn, self.student_btn, self.course_btn, self.sc_btn]
        for btn in buttons:
            btn.state(['!pressed'])

        # 设置当前按钮为激活状态
        active_button.state(['pressed'])

    def show_teacher_panel(self):
        """显示教师管理面板"""
        self.clear_content_frame()
        self.current_panel = TeacherPanel(self.content_frame)
        self.update_button_states(self.teacher_btn)
        self.content_frame.config(text="教师管理")

    def show_student_panel(self):
        """显示学生管理面板"""
        self.clear_content_frame()
        self.current_panel = StudentPanel(self.content_frame)
        self.update_button_states(self.student_btn)
        self.content_frame.config(text="学生管理")

    def show_course_panel(self):
        """显示课程管理面板"""
        self.clear_content_frame()
        self.current_panel = CoursePanel(self.content_frame)
        self.update_button_states(self.course_btn)
        self.content_frame.config(text="课程管理")

    def show_sc_panel(self):
        """显示选课管理面板"""
        self.clear_content_frame()
        self.current_panel = SCPanel(self.content_frame)
        self.update_button_states(self.sc_btn)
        self.content_frame.config(text="选课管理")
    
    def exit_application(self):
        """退出应用程序"""
        if messagebox.askokcancel("确认退出", "确定要退出学生管理系统吗？"):
            self.root.quit()

    def run(self):
        """运行主窗口"""
        self.root.mainloop()
