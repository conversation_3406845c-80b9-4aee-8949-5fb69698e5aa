# -*- coding: utf-8 -*-
"""
课程管理面板
提供课程数据的增删改查界面和分页功能（内嵌式）
"""

from views.base_panel import BasePanel
from models.course import course_model

class CoursePanel(BasePanel):
    """课程管理面板类"""

    def get_columns(self):
        """返回表格列定义"""
        return ('Cno', 'Cname', 'Term', 'Tno', 'Tname', 'Ccredit')

    def get_column_configs(self):
        """返回列配置字典"""
        return {
            'Cno': ('课程编号', 100),
            'Cname': ('课程名称', 150),
            'Term': ('学期', 60),
            'Tno': ('教师编号', 80),
            'Tname': ('教师姓名', 100),
            'Ccredit': ('学分', 60)
        }

    def get_model(self):
        """返回数据模型对象"""
        return course_model

    def get_entity_name(self):
        """返回实体名称"""
        return "课程"

    def get_dialog_class(self):
        """返回对话框类"""
        from views.course_window import CourseDialog
        return CourseDialog

    def extract_row_data(self, data_item):
        """从数据项中提取行显示数据"""
        return (
            data_item.get('Cno', ''),
            data_item.get('Cname', ''),
            data_item.get('Term', ''),
            data_item.get('Tno', ''),
            data_item.get('Tname', ''),
            data_item.get('Ccredit', '')
        )

    def get_primary_key(self, tree_item_values):
        """从表格行数据中提取主键"""
        return tree_item_values[0]  # Cno
