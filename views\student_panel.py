# -*- coding: utf-8 -*-
"""
学生管理面板
提供学生数据的增删改查界面和分页功能（内嵌式）
"""

from views.base_panel import BasePanel
from models.student import student_model

class StudentPanel(BasePanel):
    """学生管理面板类"""

    def get_columns(self):
        """返回表格列定义"""
        return ('Sno', 'Sname', 'Sdept', 'Ssex', 'Age', 'Telephone', 'Email')

    def get_column_configs(self):
        """返回列配置字典"""
        return {
            'Sno': ('学号', 100),
            'Sname': ('姓名', 100),
            'Sdept': ('系别', 150),
            'Ssex': ('性别', 60),
            'Age': ('年龄', 60),
            'Telephone': ('电话', 120),
            'Email': ('邮箱', 200)
        }

    def get_model(self):
        """返回数据模型对象"""
        return student_model

    def get_entity_name(self):
        """返回实体名称"""
        return "学生"

    def get_dialog_class(self):
        """返回对话框类"""
        from views.student_window import StudentDialog
        return StudentDialog

    def extract_row_data(self, data_item):
        """从数据项中提取行显示数据"""
        return (
            data_item.get('Sno', ''),
            data_item.get('Sname', ''),
            data_item.get('Sdept', ''),
            data_item.get('Ssex', ''),
            data_item.get('Age', ''),
            data_item.get('Telephone', ''),
            data_item.get('Email', '')
        )

    def get_primary_key(self, tree_item_values):
        """从表格行数据中提取主键"""
        return tree_item_values[0]  # Sno
